package one.srp.gensmo.viewmodel.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.network.model.EditorTag
import one.srp.core.network.model.EditorTagType
import one.srp.core.network.model.HomePageInfo
import one.srp.core.network.model.HomePageInfoStyle
import one.srp.gensmo.data.repository.MiscRepository
import one.srp.gensmo.data.repository.utils.BaseState
import one.srp.gensmo.data.remote.SearchVibeService
import one.srp.gensmo.data.remote.TryOnService
import one.srp.gensmo.data.store.UserDataStoreManager
import kotlinx.coroutines.flow.MutableStateFlow
import timber.log.Timber
import javax.inject.Inject
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.TextRange
import one.srp.gensmo.data.model.GenerationState

@HiltViewModel
class SearchExtensionViewModel @Inject constructor(
    private val repository: MiscRepository,
) : ViewModel() {
    // 添加预算选项的静态数据
    val budgetOptions = mapOf(
        "1" to EditorTag(icon = "$", text = "On a budget", type = EditorTagType.BUDGET, value = "1"),
        "2" to EditorTag(icon = "$$", text = "Sensibly priced", type = EditorTagType.BUDGET, value = "2"),
        "3" to EditorTag(icon = "$$$", text = "Upscale", type = EditorTagType.BUDGET, value = "3"),
        "4" to EditorTag(icon = "$$$$", text = "Luxury", type = EditorTagType.BUDGET, value = "4")
    )

    // 添加选中预算的状态
    private val _selectedBudget = MutableStateFlow<EditorTag?>(null)
    val selectedBudget = _selectedBudget.asStateFlow()

    // 添加选择预算的方法
    fun selectBudget(budget: EditorTag) {
        _selectedBudget.value = budget
    }

    // 添加清除选中预算的方法
    fun clearSelectedBudget() {
        _selectedBudget.value = null
    }

    private val _modelUrl = MutableStateFlow<String?>(null)
    val modelUrl = _modelUrl.asStateFlow()

    private val _homeInfo = MutableStateFlow<BaseState<HomePageInfo>>(BaseState.Loading)
    val homeInfo = _homeInfo.asStateFlow()

    private val _imaginationResult = MutableStateFlow<List<String>>(emptyList())
    val imaginationResult = _imaginationResult.asStateFlow()

    // 添加随机建议的StateFlow
    private val _randomSuggestions = MutableStateFlow<List<String>>(emptyList())
    val randomSuggestions = _randomSuggestions.asStateFlow()

    // 添加选中的 styles 状态
    private val _selectedStyles = MutableStateFlow<List<HomePageInfoStyle>>(emptyList())
    val selectedStyles = _selectedStyles.asStateFlow()

    // 添加历史状态变量
    private var _previousQuery: String? = null
    private var _previousBudget: EditorTag? = null
    private var _previousStyles: List<HomePageInfoStyle> = emptyList()

    private val _searchQueryValue = MutableStateFlow<TextFieldValue>(TextFieldValue(""))
    val searchQueryValue = _searchQueryValue.asStateFlow()

    // 添加生成状态
    private val _generationState = MutableStateFlow(GenerationState.IDLE)
    val generationState = _generationState.asStateFlow()

    // 添加输入框焦点状态
    private val _isTextFieldFocused = MutableStateFlow(false)
    val isTextFieldFocused = _isTextFieldFocused.asStateFlow()

    // 添加键盘显示状态
    private val _hasKeyboardBeenShown = MutableStateFlow(false)
    val hasKeyboardBeenShown = _hasKeyboardBeenShown.asStateFlow()

    // 添加 displayImageUrl 状态
    private val _displayImageUrl = MutableStateFlow<String?>(null)
    val displayImageUrl = _displayImageUrl.asStateFlow()

    // 添加更新 displayImageUrl 的方法
    fun updateDisplayImageUrl(url: String?) {
        _displayImageUrl.value = url
    }

    // 添加更新搜索查询的方法
    fun updateSearchQuery(value: TextFieldValue) {
        _searchQueryValue.value = value
    }

    // 添加撤销方法
    fun undoQueryPolishing() {
        _previousQuery?.let { query ->
            _searchQueryValue.value = TextFieldValue(query, TextRange(query.length))
        }
        _selectedBudget.value = _previousBudget
        _previousStyles.let { styles ->
            _selectedStyles.value = styles
        }
        _generationState.value = GenerationState.IDLE
    }

    // 添加处理 style 选择的方法
    fun toggleStyle(style: HomePageInfoStyle) {
        val currentStyles = _selectedStyles.value.toMutableList()
        // 检查当前style是否已被选中
        val isSelected = currentStyles.any { it.title == style.title }
        
        // 检查style是否来自chooseAStyle
        val isFromChooseAStyle = when (val homeInfoState = _homeInfo.value) {
            is BaseState.Success -> homeInfoState.data.chooseAStyle?.any { it.title == style.title } == true
            else -> false
        }
        
        if (isFromChooseAStyle) {
            // 如果来自chooseAStyle，实现单选逻辑
            // 先移除所有来自chooseAStyle的标签
            val chooseAStyleTitles = when (val homeInfoState = _homeInfo.value) {
                is BaseState.Success -> homeInfoState.data.chooseAStyle?.map { it.title } ?: emptyList()
                else -> emptyList()
            }
            currentStyles.removeAll { it.title in chooseAStyleTitles }
            
            // 如果当前标签未被选中，则添加它
            if (!isSelected) {
                currentStyles.add(style)
            }
        } else {
            // 如果不是来自chooseAStyle，实现切换逻辑
            if (isSelected) {
                // 如果已选中，则移除
                currentStyles.removeAll { it.title == style.title }
            } else {
                // 如果未选中，则添加
                currentStyles.add(style)
            }
        }
        
        _selectedStyles.value = currentStyles
    }

    fun fetchHomeInfo() = viewModelScope.launch {
        repository.getHomepageInfo().collect { result ->
            when {
                result.isSuccess -> _homeInfo.value =
                    result.getOrNull()?.let { BaseState.Success(it) }
                        ?: BaseState.Error(result.exceptionOrNull())

                result.isFailure -> _homeInfo.value = BaseState.Error(result.exceptionOrNull())
            }
        }
    }

    /**
     * 从查询建议中随机选取2个建议
     * @return 包含2个随机建议的列表，若建议总数少于2个则返回所有可用建议
     */
    fun getRandomSuggestions() {
        val currentSuggestions = _imaginationResult.value
        val randomSuggestions = when {
            currentSuggestions.isEmpty() -> emptyList()
            currentSuggestions.size == 1 -> currentSuggestions
            else -> currentSuggestions.shuffled().take(2)
        }
        _randomSuggestions.value = randomSuggestions
    }

    /**
     * 获取联想词查询
     */
    fun getImaginationQuery() {
        viewModelScope.launch {
            try {
                val response = SearchVibeService.api.imaginationQuery()
                if (response.isSuccessful) {
                    response.body()?.let { responseBody ->
                        _imaginationResult.emit(responseBody.list)
                        // 获取到联想词后自动刷新随机建议
                        getRandomSuggestions()
                        Timber.d("获取联想词成功: ${responseBody.list}")
                    }
                } else {
                    Timber.e("获取联想词失败: ${response.errorBody()?.string()}")
                }
            } catch (e: Exception) {
                Timber.e(e, "获取联想词发生错误")
                _imaginationResult.emit(emptyList())
                _randomSuggestions.emit(emptyList())
            }
        }
    }

    private fun refreshModelFromApi() {
        viewModelScope.launch {
            if (!UserDataStoreManager.isUserLoggedIn()) {
                Timber.d("无法刷新模型：用户未登录")
                return@launch
            }
            
            try {
                val response = TryOnService.api.getReplica()
                if (response.isSuccessful) {
                    val body = response.body()
                    if (body == null) {
                        _modelUrl.value = null
                        return@launch
                    }
                    
                    val url = body.modelUrl
                    val id = body.modelId
                    _modelUrl.value = url
                    UserDataStoreManager.saveModelInfo(url, id)
                    Timber.d("模型信息已刷新：$url")
                }
            } catch (e: Exception) {
                Timber.e(e, "刷新模型信息出错")
            }
        }
    }
    
    // 修改loadModelInfo函数，使用新的刷新函数
    fun loadModelInfo() {
        viewModelScope.launch {
            if (UserDataStoreManager.isUserLoggedIn()) {
                // 首先从 UserDataStore 获取
                val (modelUrl, modelId) = UserDataStoreManager.getModelInfo()
                // 如果本地没有存储的 URL，则从 API 获取
                if (modelUrl.isNullOrEmpty() || modelId.isNullOrEmpty()) {
                    refreshModelFromApi()
                } else {
                    _modelUrl.value = modelUrl
                }
            } else {
                Timber.d("user not logged in")
                _modelUrl.value = null
            }
        }
    }

    // 添加重置状态的方法
    fun resetState() {
        _searchQueryValue.value = TextFieldValue("")
        _selectedStyles.value = emptyList()
        _selectedBudget.value = null
        _generationState.value = GenerationState.IDLE
        _previousQuery = null
        _previousBudget = null
        _previousStyles = emptyList()
        _isTextFieldFocused.value = false
        _hasKeyboardBeenShown.value = false
        _displayImageUrl.value = null  // 重置 displayImageUrl
    }

    // 添加更新焦点状态的方法
    fun updateTextFieldFocus(focused: Boolean) {
        _isTextFieldFocused.value = focused
        if (focused) {
            _hasKeyboardBeenShown.value = true
        }
    }

    fun queryPolishing(query: String) {
        viewModelScope.launch {
            // 更新状态为生成中
            _generationState.value = GenerationState.GENERATING
            
            // 保存当前值到历史状态
            _previousQuery = query
            _previousBudget = _selectedBudget.value
            _previousStyles = _selectedStyles.value.toList()

            val response = SearchVibeService.api.queryPolishing(query)
            if (response.isSuccessful) {
                response.body()?.let { responseBody ->
                    // 更新搜索查询文本
                    responseBody.text.let { newText ->
                        _searchQueryValue.value = TextFieldValue(newText, TextRange(newText.length))
                    }
                    
                    // 更新预算
                    responseBody.budget.let { budgetValue ->
                        budgetOptions[budgetValue]?.let { budget ->
                            _selectedBudget.value = budget
                        }
                    }
                    
                    // 更新样式 - 允许多个样式（来自API响应）
                    responseBody.style.let { styleTitles ->
                        val newStyles = styleTitles.map { title ->
                            HomePageInfoStyle(
                                title = title,
                                image = null  // 图片可以为空
                            )
                        }
                        _selectedStyles.value = newStyles
                    }
                    
                    // 更新状态为完成
                    _generationState.value = GenerationState.COMPLETED
                } ?: run {
                    // 当 responseBody 为空时，重置状态为空闲
                    _generationState.value = GenerationState.IDLE
                }
            } else {
                Timber.e("query polishing failed: ${response.errorBody()?.string()}")
                // 发生错误时重置状态为空闲
                _generationState.value = GenerationState.IDLE
            }
        }
    }
}
