package one.srp.gensmo.ui.screens.detail._components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.MoodboardContentBlock
import one.srp.core.network.model.MoodboardEntity
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.ui.screens.product.panel.info.ProductInfoDrawer
import one.srp.gensmo.utils.metrics.MetricViewModel

@Composable
fun ProductAction(
    item: MoodboardContentBlock?,
    moodboard: MoodboardEntity,
    onClose: () -> Unit = {},
    onRemix: (ProductItem) -> Unit = {},
    onTryOn: (ProductItem) -> Unit = {},
) {
    if (item == null) return

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.ProductDetail)

    val product = remember(moodboard, item) { moodboard.products.find { it.id == item.id } }

    val remixProduct = { product: ProductItem ->
        metric(
            SelectItem(
                EventItemListName.RemixBtn, method = EventMethod.Click, items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.Product,
                        itemId = product.globalId,
                        itemName = product.brand ?: "Curated pick"
                    )
                )
            )
        )
        onClose()
        onRemix(product)
    }

    ProductInfoDrawer(
        open = product != null,
        onClose = { onClose() },
        item = product,
        onRemix = {
            product?.let { remixProduct(it) }
        },
        onTryOn = {
            onClose()
            product?.let { onTryOn(it) }
        },
        tryOnEnabled = product?.tags?.cateTag in listOf(
            "coat",
            "top",
            "bottom",
            "full-body",
            "ootd"
        ),
        refer = EventRefer.ProductDetail,
    )
}
