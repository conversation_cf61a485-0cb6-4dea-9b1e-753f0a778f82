package one.srp.gensmo.ui.screens.feed._components


import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.toColorInt
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import kotlinx.serialization.Serializable
import one.srp.core.network.model.FeedItem
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.R
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.ui.theme.appThemeShapes
import one.srp.gensmo.utils.mime.resizeImage
import one.srp.gensmo.viewmodel.tryon.CreateViewModel

@Composable
fun FeedCard(
    modifier: Modifier = Modifier,
    item: FeedItem,
    onItemClick: () -> Unit = {},
    liked: Boolean = false,
    onLikeClick: () -> Unit = {},
) {
    val isTryOn = item.tryOnTaskId?.isNotBlank() == true

    val context = LocalContext.current
    val createViewModel: CreateViewModel =
        hiltViewModel(key = "FeedCard" + (item.tryOnTaskId ?: item.moodboardId))
    val uiState by createViewModel.uiState.collectAsState()

    fun clickLikeWithCheck() {
        if (createViewModel.isUserLoggedIn()) {
            onLikeClick()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("default_avatar")
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onItemClick() }
            .then(modifier),
        shape = appThemeShapes.small,
        colors = CardDefaults.cardColors(MaterialTheme.colorScheme.background),
    ) {
        Column(
            modifier = Modifier
                .padding(4.dp, 0.dp)
                .padding(bottom = 4.dp)
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current).data(
                    resizeImage(
                        (if (isTryOn) (item.tryOnCoverImage ?: item.tryOnUrl) else item.image)
                            ?: "",
                        400
                    )
                ).crossfade(true).build(),
                contentDescription = "feed",
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(
                        item.width?.takeIf { it > 0 }?.let { w ->
                            item.height?.takeIf { it > 0 }?.let { h -> w / h }
                        } ?: 0.6f
                    ),
                contentScale = ContentScale.Crop
            )

            item.detailTitle?.let { detailTitle ->
                Text(
                    text = detailTitle,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(4.dp),
                    style = AppThemeTextStyle.Body12H,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 2,
                )
            }

            Row(
                modifier = Modifier
                    .padding(4.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    item.userInfo?.let { userInfo ->
                        AsyncImage(
                            model = userInfo.userImageUrl,
                            contentDescription = null,
                            modifier = Modifier
                                .size(18.dp)
                                .clip(CircleShape),
                            contentScale = ContentScale.Crop
                        )

                        Text(
                            text = userInfo.userName,
                            style = AppThemeTextStyle.Body12H.copy(AppThemeColors.Gray600),
                        )
                    }
                }

                item.likedCount?.let {
                    Card(
                        colors = CardDefaults.cardColors(Color.Transparent)
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            Icon(
                                painter = painterResource(
                                    id = if (liked) R.drawable.icon_heart_red
                                    else R.drawable.icon_heart_feed
                                ),
                                contentDescription = null,
                                modifier = Modifier
                                    .size(14.dp)
                                    .clickable {
                                        clickLikeWithCheck()
                                    },
                                tint = Color.Unspecified
                            )

                            if (it > 0) {
                                Text(
                                    text = "$it",
                                    style = AppThemeTextStyle.Body12H.copy(AppThemeColors.Gray600)
                                )
                            }
                        }
                    }

                    if (uiState.showLoginDialog) {
                        LoginModal(
                            onDismiss = { createViewModel.updateLoginDialog(false) },
                            isLoading = uiState.isLoading,
                            onGoogleLogin = {
                                createViewModel.loginWithGoogle(
                                    context = context,
                                    onSuccess = {
                                        onLikeClick()
                                    }
                                )
                                createViewModel.updateLoginDialog(false)
                            },
                            onAppleLogin = {
                                createViewModel.loginWithApple(
                                    context = context,
                                    onSuccess = {
                                        onLikeClick()
                                    }
                                )
                                createViewModel.updateLoginDialog(false)
                            }
                        )
                    }
                }
            }
        }
    }
}


@Composable
fun FeedCardLegacy(
    modifier: Modifier = Modifier,
    item: FeedItem,
    onItemClick: () -> Unit = {},
    onItemTryOn: () -> Unit = {},
) {
    val isTryOnFeed = item.tryOnTaskId?.isNotBlank() == true

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = { onItemClick() }),
        shape = RoundedCornerShape(2.dp),

        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column {
            Box {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current).data(
                        resizeImage(
                            (if (isTryOnFeed) item.tryOnCoverImage else item.image) ?: "", 400
                        )
                    ).crossfade(true).build(),
                    contentDescription = "feed",
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(
                            item.width?.takeIf { it > 0 }?.let { w ->
                                item.height?.takeIf { it > 0 }?.let { h -> w / h }
                            } ?: 0.6f
                        ),
                    contentScale = ContentScale.Crop
                )
            }

            Text(
                text = item.reasoning ?: "",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp, 12.dp, 16.dp, 8.dp),
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 13.sp,
                    lineHeight = 18.2.sp,
                    fontWeight = FontWeight(300),
                    color = Color(0xFF222222),
                ),
            )

            if (isTryOnFeed || item.isTryOn == true) {
                Row(
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                ) {
                    Button(
                        onClick = { onItemTryOn() },
                        colors = ButtonDefaults.buttonColors(item.buttonColor?.let { Color(it.toColorInt()) }
                            ?: Color.LightGray.copy(0.4f), Color.Black),
                        contentPadding = PaddingValues(2.dp),
                        shape = RoundedCornerShape(4.dp),
                        modifier = Modifier.weight(1f),
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Row {
                                (item.moodboards?.products ?: item.products)?.let {
                                    parseTryOnProducts(it).products.mapIndexed { idx, p ->
                                        AsyncImage(
                                            model = ImageRequest.Builder(LocalContext.current)
                                                .data(p.mainImage?.link?.let {
                                                    resizeImage(it, 100)
                                                }).build(),
                                            contentDescription = null,
                                            modifier = Modifier.size(32.dp)
                                        )
                                    }
                                }
                            }
                            Text(
                                stringResource(R.string.text_try_on).uppercase(),
                                modifier = Modifier.weight(1f),
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontSize = 12.sp,
                                    lineHeight = 20.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF242424),
                                ),
                            )
                        }
                    }
                }
            } else {
                Row(
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                ) {
                    Button(
                        onClick = { onItemClick() },
                        colors = ButtonDefaults.buttonColors(item.buttonColor?.let { Color(it.toColorInt()) }
                            ?: Color.LightGray.copy(0.4f), Color.Black),
                        contentPadding = PaddingValues(2.dp),
                        shape = RoundedCornerShape(4.dp),
                        modifier = Modifier.weight(1f),
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Row {
                                (item.moodboards?.products ?: item.products)?.let {
                                    parseTryOnProducts(it).products.mapIndexed { idx, p ->
                                        AsyncImage(
                                            model = ImageRequest.Builder(LocalContext.current)
                                                .data(p.mainImage?.link?.let {
                                                    resizeImage(it, 100)
                                                }).build(),
                                            contentDescription = null,
                                            modifier = Modifier.size(32.dp)
                                        )
                                    }
                                }
                            }
                            Text(
                                text = item.buttonText
                                    ?: stringResource(R.string.text_remix).uppercase(),
                                modifier = Modifier.weight(1f),
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontSize = 12.sp,
                                    lineHeight = 20.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF242424),
                                ),
                            )
                        }
                    }
                }
            }

            Row(
                modifier = Modifier
                    .padding(horizontal = 8.dp, vertical = 8.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 用户信息
                item.userInfo?.let { userInfo ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(userInfo.userImageUrl).build(),
                            contentDescription = null,
                            modifier = Modifier
                                .size(24.dp)
                                .clip(CircleShape),
                            contentScale = ContentScale.Crop
                        )
                        Text(
                            text = userInfo.userName,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontSize = 12.sp, color = Color(0xFF666666)
                            )
                        )
                    }
                }

                // Remix 计数
                item.remix?.let {
                    if (it > 0) {
                        Card(
                            modifier = Modifier.padding(0.dp),
                            colors = CardDefaults.cardColors(containerColor = Color.Transparent)
                        ) {
                            Row(
                                modifier = Modifier.padding(horizontal = 4.dp),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.icon_heart),
                                    contentDescription = null,
                                    tint = Color(0xFF555555),
                                    modifier = Modifier.size(16.dp)
                                )
                                Text(
                                    text = "$it", color = Color(0xFF555555), fontSize = 12.sp
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Serializable
data class ProductGroup(
    val tag: String,
    val products: List<ProductItem>,
)

fun parseTryOnProducts(products: List<ProductItem>): ProductGroup {
    val fullBody = products.filter { p -> p.tags?.cateTag == "full-body" }
    if (!fullBody.isEmpty()) return ProductGroup(
        tag = "full-body", products = listOfNotNull(fullBody.getOrNull(0))
    )

    val coat = products.filter { p -> p.tags?.cateTag == "coat" }
    val top = products.filter { p -> p.tags?.cateTag == "top" }
    val bottom = products.filter { p -> p.tags?.cateTag == "bottom" }

    if (!coat.isEmpty()) return ProductGroup(
        tag = "coat_bottom", products = listOfNotNull(coat.getOrNull(0), bottom.getOrNull(0))
    )

    return ProductGroup(
        tag = "top_bottom", products = listOfNotNull(top.getOrNull(0), bottom.getOrNull(0))
    )
}
