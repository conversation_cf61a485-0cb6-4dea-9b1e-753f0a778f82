package one.srp.gensmo.ui.screens.session.chat._components

import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import one.srp.gensmo.R
import one.srp.core.network.model.CollectionType
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.viewmodel.tryon.CreateViewModel
import timber.log.Timber

@Composable
fun SaveActionContainer(
    type: CollectionType,
    id: String,
    initialState: Boolean = false,
    onPreAction: (Boolean) -> Unit = {},
    onAction: (Boolean) -> Unit = {},
    content: @Composable (Boolean, () -> Unit) -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()

    var saved by remember(id, initialState) { mutableStateOf(initialState) }
    fun clickSave() {
        coroutineScope.launch {
            val newSaved = !saved
            saved = newSaved

            try {
                onPreAction(newSaved)
                onAction(newSaved)

                if (newSaved) {
                    UserService.api.postSaved(id, type)
                } else {
                    UserService.api.deleteSaved(id, type)
                }
            } catch (e: Exception) {
                Timber.e(e)
                saved = !newSaved
                onAction(!newSaved)
            }
        }
    }

    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = ("SaveAction_$id"))
    val uiState by createViewModel.uiState.collectAsState()

    fun clickSaveWithCheck() {
        if (createViewModel.isUserLoggedIn()) {
            clickSave()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("default_avatar")
        }
    }

    content(saved) { clickSaveWithCheck() }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        clickSave()
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        clickSave()
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }
}

@Composable
fun SaveIconButton(
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color.White,
    enable: Boolean = false,
    onClick: () -> Unit = {},
) {
    IconButton(
        modifier = modifier,
        onClick = onClick,
        colors = IconButtonDefaults.iconButtonColors(backgroundColor),
    ) {
        Icon(
            painterResource(if (enable) R.drawable.icon_saved_black else R.drawable.icon_saved_line),
            null
        )
    }
}
