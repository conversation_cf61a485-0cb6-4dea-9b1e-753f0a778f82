package one.srp.gensmo.ui.screens.feed._components

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged

@OptIn(ExperimentalMaterial3Api::class, FlowPreview::class)
@Composable
fun <T : Any> FeedList(
    modifier: Modifier = Modifier,
    items: LazyPagingItems<T>,
    refreshable: Boolean = true,
    onScroll: (Int) -> Unit = {},
    content: @Composable (item: T) -> Unit,
) {
    val lazyGridState = rememberLazyStaggeredGridState()

    LaunchedEffect(lazyGridState) {
        snapshotFlow { lazyGridState.firstVisibleItemIndex }
            .distinctUntilChanged()
            .debounce(300)
            .collect { index ->
                onScroll(index)
            }
    }

    val feedContent: @Composable () -> Unit = {
        if (items.itemCount == 0) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(100.dp)
            )
        } else {
            LazyVerticalStaggeredGrid(
                columns = StaggeredGridCells.Fixed(2),
                state = lazyGridState,
                modifier = modifier,
            ) {
                items(items.itemCount) { index ->
                    items[index]?.let {
                        content(it)
                    }
                }
            }

            when (items.loadState.append) {
                is LoadState.Error -> {
                    val e = items.loadState.append as LoadState.Error
                    Text("$e")
                }

                else -> {}
            }
        }
    }

    if (refreshable) {
        PullToRefreshBox(
            modifier = Modifier.fillMaxSize(),
            isRefreshing = items.loadState.refresh is LoadState.Loading,
            onRefresh = { items.refresh() },
        ) {
            feedContent()
        }
    } else {
        feedContent()
    }
}
