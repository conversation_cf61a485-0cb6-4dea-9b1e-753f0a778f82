package one.srp.gensmo.ui.screens.product.panel.info

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.CollectionType
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.ui.screens.product.panel._components.ProductAction
import one.srp.gensmo.ui.screens.product.panel._components.ProductDetail
import one.srp.gensmo.ui.screens.product.panel._components.ProductDrawer
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricViewModel
import timber.log.Timber

@Composable
fun ProductInfoDrawer(
    open: Boolean = false,
    item: ProductItem? = null,
    onClose: () -> Unit,
    onTryOn: () -> Unit = {},
    onRemix: () -> Unit = {},
    onPostSave: () -> Unit = {},
    tryOnEnabled: Boolean = true,
    refer: EventRefer = EventRefer.ProductDetail,
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)
    // 埋点：商品详情页页面全局曝光
    LaunchedEffect(open) {
        if (open) {
            metric(
                SelectItem(
                    itemListName = EventItemListName.Screen,
                    method = EventMethod.PageView
                )
            )
            // 埋点：商品详情页默认选项展示
            metric(
                ViewItem(
                    refer = refer,
                    itemListName = EventItemListName.Default,
                    items = listOf(
                        EventItem(
                            itemCategory = EventItemCategory.Product,
                            itemId = item?.globalId,
                            itemName = item?.title
                        )
                    )
                )
            )
        }
    }

    val urlHandler = LocalUriHandler.current
    val linkValid = item?.link?.let {
        val uri = it.toUri()
        uri.scheme != null && uri.host != null
    } == true

    fun openShopLink() {
        item?.let { item ->
            if (linkValid) {
                try {
                    metric(
                        SelectItem(
                            method = EventMethod.Click,
                            itemListName = EventItemListName.ProductCard,
                            actionType = EventActionType.ProductExternalJump,
                            items = listOf(
                                EventItem(
                                    itemId = item.id,
                                    itemName = item.title,
                                    itemCategory = EventItemCategory.Product,
                                )
                            )
                        )
                    )
                    MetricData.logEventAF("af_product_external_jump")

                    urlHandler.openUri(item.link ?: "/")
                } catch (err: Exception) {
                    Timber.e(err)
                }
            }
        }
    }

    val coroutineScope = rememberCoroutineScope()
    var saved by remember(item) { mutableStateOf(item?.isFavorited == true) }
    fun saveProduct() {
        coroutineScope.launch {
            item?.globalId?.let {
                val newSaved = !saved
                saved = newSaved
                item.isFavorited = newSaved
                onPostSave()

                try {
                    if (newSaved) {
                        UserService.api.postSaved(it, CollectionType.Product)
                    } else {
                        UserService.api.deleteSaved(it, CollectionType.Product)
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    saved = !newSaved
                    item.isFavorited = !newSaved
                }
            }
        }
    }

    ProductDrawer(open = open, onClose = onClose) {
        item?.let { item ->
            Box() {
                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                    ProductDetail(
                        item = item,
                        saved = saved,
                        replaceable = false,
                        onSave = { saveProduct() },
                        onOutJump = { openShopLink() },
                        onTryOn = { onTryOn() },
                        tryOnEnabled = tryOnEnabled,
                        refer = refer,
                    )

                    ProductAction(
                        item = item,
                        onRemix = { onRemix() },
                        onShopNow = { openShopLink() },
                        shopNowEnabled = linkValid,
                        refer = refer
                    )
                }

                Box(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(12.dp)
                ) {
                    IconButton(
                        colors = IconButtonDefaults.iconButtonColors(
                            Color.Transparent
                        ),
                        onClick = { onClose() }
                    ) {
                        Icon(Icons.Default.Close, null)
                    }
                }
            }
        }
    }
}
