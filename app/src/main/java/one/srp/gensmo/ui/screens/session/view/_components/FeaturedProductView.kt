package one.srp.gensmo.ui.screens.session.view._components

import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowOutward
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel

@Composable
fun FeaturedProductView(
    refer: EventRefer,
    products: List<ProductItem>,
    onProductClick: (ProductItem) -> Unit = {},
    collageGenTaskId: String? = null,
    generalCollageId: String? = null,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        if (products.isNotEmpty()) {
            Text(
                text = stringResource(R.string.text_featured_products),
                style = AppThemeTextStyle.Heading20D,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            items(products) { item ->
                FeaturedProductCard(
                    refer = refer,
                    modifier = Modifier.width(140.dp),
                    item = item,
                    onItemClick = onProductClick,
                    collageGenTaskId = collageGenTaskId,
                    generalCollageId = generalCollageId
                )
            }
        }
    }
}

@Composable
fun FeaturedProductCard(
    modifier: Modifier = Modifier,
    refer: EventRefer,
    item: ProductItem,
    onItemClick: (ProductItem) -> Unit = {},
    collageGenTaskId: String? = null,
    generalCollageId: String? = null,
) {
    // Metric helper for this view, allows different refer
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)
    val context = LocalContext.current

    Column(
        modifier = modifier.clickable {
            metric(
                SelectItem(
                    itemListName = EventItemListName.ProductListProduct,
                    method = EventMethod.Click,
                    actionType = EventActionType.ProductExternalJump,
                    items = buildList {
                        add(
                            EventItem(
                                itemCategory = EventItemCategory.Product,
                                itemId = item.globalId,
                                itemName = item.title ?: ""
                            )
                        )
                        collageGenTaskId?.let { taskId ->
                            add(
                                EventItem(
                                    itemCategory = EventItemCategory.CollageGenTask,
                                    itemId = taskId,
                                    itemName = ""
                                )
                            )
                        }
                        generalCollageId?.let { collageId ->
                            add(
                                EventItem(
                                    itemCategory = EventItemCategory.GeneralCollage,
                                    itemId = collageId,
                                    itemName = ""
                                )
                            )
                        }
                    }
                )
            )

            // 尝试打开外部链接
            item.link?.let { link ->
                try {
                    val intent = Intent(Intent.ACTION_VIEW, link.toUri())
                    context.startActivity(intent)
                } catch (e: Exception) {
                    // 如果打开外部链接失败，回调到原始处理逻辑
                    onItemClick(item)
                }
            } ?: run {
                // 如果没有链接，回调到原始处理逻辑
                onItemClick(item)
            }
        },
    ) {
        Column(
            modifier = Modifier
                .aspectRatio(0.8f)
                .background(MaterialTheme.colorScheme.surface)
                .clip(MaterialTheme.shapes.small),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Column(modifier = Modifier.padding(2.dp)) {
                AsyncImage(item.mainImage?.link, null)
            }
        }

        Column(
            modifier = Modifier.padding(4.dp, 6.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            Text(
                text = when {
                    item.brand.isNullOrEmpty() -> "Curated pick"
                    !item.brandParentName.isNullOrEmpty() -> "${item.brand} by ${item.brandParentName}"
                    else -> item.brand ?: "Curated pick"
                },
                style = AppThemeTextStyle.Heading16H,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )

            item.title?.let { title ->
                Text(
                    text = title,
                    style = AppThemeTextStyle.Body12LightH.copy(AppThemeColors.Gray700),
                    minLines = 2,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                )
            }

            Row(
                modifier = Modifier
                    .background(
                        color = Color(0xFFF5F5F5),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .padding(start = 4.dp, top = 4.dp, end = 4.dp, bottom = 4.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AsyncImage(
                    model = item.sourceIcon,
                    contentDescription = null,
                    modifier = Modifier.size(12.dp)
                )
                Text(
                    text = item.buyboxWinner?.price?.raw ?: "",
                    style = AppThemeTextStyle.Body11LightH
                )
                Icon(
                    Icons.Default.ArrowOutward,
                    null,
                    modifier = Modifier.size(16.dp),
                    tint = Color(0xFF6F6F6F)
                )
            }
        }
    }
} 