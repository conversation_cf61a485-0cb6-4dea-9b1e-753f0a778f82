package one.srp.gensmo.ui.screens.detail.hashtag

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.FeedItem
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.detail.hashtag._components.HashtagFeedFlow
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.hashtag.HashtagDetailViewModel
import timber.log.Timber

@Composable
fun HashtagDetailScreen(
    hashtag: String,
    navActions: NavActions = NavActions(),
    viewModel: HashtagDetailViewModel = hiltViewModel(),
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.HashtagDetail)

    LaunchedEffect(hashtag) {
        metric(SelectItem(EventItemListName.Screen, method = EventMethod.PageView))
        MetricData.logEventAF("af_hashtag_detail_view")
        Timber.d("HashtagDetailScreen opened for hashtag: $hashtag")
    }

    // 拦截系统返回按钮，确保使用我们的安全返回逻辑
    BackHandler {
        navActions.back()
    }

    fun genEventItemByFeedType(item: FeedItem): EventItem {
        return (if (item.tryOnTaskId?.isNotBlank() == true) EventItem(
            itemCategory = EventItemCategory.TryOnCollage,
            itemId = item.tryOnTaskId,
            itemName = item.reasoning
        )
        else EventItem(
            itemCategory = EventItemCategory.GeneralCollage,
            itemId = item.moodboardId,
            itemName = item.reasoning
        ))
    }

    fun onHashtagFeedLoadMore(list: List<FeedItem> = emptyList()) {
        metric(
            ViewItemList(
                itemListName = EventItemListName.ApHashtagFeedList,
                items = list.map { item -> genEventItemByFeedType(item) }
            )
        )
    }

    fun clickFeedItem(item: FeedItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.ApHashtagFeedListItem,
                method = EventMethod.Click,
                actionType = EventActionType.EnterFeedDetail,
                items = listOf(genEventItemByFeedType(item)) + EventItem(
                    itemId = hashtag,
                    itemName = hashtag,
                    itemCategory = EventItemCategory.Hashtag,
                )
            )
        )
        // 导航到详情页面
        if (item.tryOnTaskId?.isNotBlank() == true) {
            navActions.navigateToTryOnDetail(item.tryOnTaskId ?: "", "hashtag")
        } else {
            navActions.navigateToFeedDetail(item.moodboardId, "hashtag")
        }
    }

    fun onHashtagFeedItemView(item: FeedItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.ApHashtagFeedList,
                method = EventMethod.TrueViewTrigger,
                items = listOf(genEventItemByFeedType(item)) + EventItem(
                    itemId = hashtag,
                    itemName = hashtag,
                    itemCategory = EventItemCategory.Hashtag,
                )
            )
        )
    }

    Scaffold(
        topBar = {
            TopBar(
                transparent = true,
                onBack = { navActions.back() }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = paddingValues.calculateTopPadding())
        ) {
            // 标签信息区域
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                Text(
                    text = "#$hashtag",
                    style = AppThemeTextStyle.Heading16H
                )
            }

            // Feed内容区域
            HashtagFeedFlow(
                modifier = Modifier.weight(1f),
                viewModel = viewModel,
                hashtag = hashtag,
                onItemClick = { clickFeedItem(it) },
                onLoadMore = { onHashtagFeedLoadMore(it) },
                onItemView = { onHashtagFeedItemView(it) }
            )

            Spacer(modifier = Modifier.height(paddingValues.calculateBottomPadding()))
        }
    }
}
