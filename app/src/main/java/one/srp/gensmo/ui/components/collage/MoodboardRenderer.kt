package one.srp.gensmo.ui.components.collage

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.core.graphics.toColorInt
import coil3.compose.AsyncImage
import coil3.compose.AsyncImagePainter
import coil3.request.ImageRequest
import coil3.request.allowHardware
import coil3.request.crossfade
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import one.srp.core.network.model.MoodboardContent
import one.srp.core.network.model.MoodboardContentBlock
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeFontFamily

@Composable
fun MoodboardRenderer(
    modifier: Modifier = Modifier,
    item: MoodboardContent? = null,
    onItemClick: (MoodboardContentBlock) -> Unit = {},
    offscreen: Boolean = false,
    animate: Boolean = false,
    interactive: Boolean = false,
    boundary: String = "w",
    showTags: Boolean = false,
) {
    // 标签轮播状态管理
    var currentTagIndex by remember { mutableIntStateOf(0) }

    // 获取所有有标签的blocks
    val taggedBlocks = remember(item) {
        item?.blocks?.filter { it.brandLink != null || it.text != null } ?: emptyList()
    }

    // 标签轮播定时器
    LaunchedEffect(showTags, taggedBlocks.size) {
        if (showTags && taggedBlocks.isNotEmpty()) {
            while (true) {
                delay(2000) // 每2秒切换到下一个标签
                currentTagIndex = (currentTagIndex + 1) % taggedBlocks.size
            }
        }
    }
    BoxWithConstraints(modifier = Modifier.wrapContentSize()) {
        item?.let {
            val contentWidth = item.width
            val contentHeight = item.height ?: (contentWidth * 1.5f)

            val containerWidth = this.maxWidth.value
            val containerHeight = this.maxHeight.value
            val zoom =
                if (boundary == "h") containerHeight / contentHeight else containerWidth / contentWidth
            val fontZoom =
                if (boundary == "h") this.maxHeight / LocalConfiguration.current.screenHeightDp.dp else this.maxWidth / LocalConfiguration.current.screenWidthDp.dp

            val sortedBlocks = item.blocks.sortedBy { it.position.z }
            val backgroundIsImage =
                item.background?.let { item.background?.startsWith("#") ?: false } ?: false
            val backgroundColor = if (backgroundIsImage) Color.Transparent else Color(
                (item.background?.trim() ?: "#ffffff").toColorInt()
            )

            Box(
                modifier = Modifier
                    .aspectRatio(contentWidth / contentHeight)
                    .background(backgroundColor)
                    .then(modifier)
            ) {
                item.background?.let {
                    if (backgroundIsImage) {
                        BackgroundBlock(url = item.background ?: "", offscreen = offscreen)
                    }
                }

                sortedBlocks.forEach { block ->
                    when (block.content.type) {
                        "image" -> {
                            val shouldShowTagContent = showTags &&
                                    taggedBlocks.isNotEmpty() &&
                                    taggedBlocks.getOrNull(currentTagIndex) == block

                            ImageBlock(
                                block = block,
                                zoom = zoom,
                                onItemClick = onItemClick,
                                offscreen = offscreen,
                                animate = animate,
                                interactive = interactive,
                                showTags = showTags,
                                showTagContent = shouldShowTagContent,
                            )
                        }

                        "text" -> TextBlock(block = block, zoom = zoom, fontZoom = fontZoom)

                        else -> {}
                    }
                }
            }
        }
    }
}

@Composable
fun BackgroundBlock(url: String, offscreen: Boolean = false) {
    AsyncImage(
        model = ImageRequest.Builder(LocalContext.current).data(url).crossfade(true)
            .allowHardware(offscreen).build(),
        contentDescription = null,
        contentScale = ContentScale.FillBounds,
        modifier = Modifier.fillMaxSize(),
    )
}

@Composable
fun ImageBlock(
    block: MoodboardContentBlock,
    zoom: Float = 1f,
    onItemClick: (MoodboardContentBlock) -> Unit = {},
    offscreen: Boolean = false,
    animate: Boolean = false,
    interactive: Boolean = false,
    showTags: Boolean = false,
    showTagContent: Boolean = false,
) {
    val coroutineScope = rememberCoroutineScope()

    val offsetX = (block.position.x ?: 0f) * zoom
    val offsetY = (block.position.y ?: 1f) * zoom
    val blockWidth = (block.size.width ?: 1f) * zoom
    val blockHeight = (block.size.height ?: 1f) * zoom

    var success by remember { mutableStateOf(false) }
    val scale = remember { Animatable(1f) }

    Box(
        modifier = Modifier
            .size(blockWidth.dp, blockHeight.dp)
            .offset(x = offsetX.dp, y = offsetY.dp)
            .zIndex((block.position.z ?: 0f))
            .then(if (!animate) Modifier else Modifier.scale(scale.value))
            .then(if (offscreen || !interactive) Modifier else Modifier.clickable {
                onItemClick(block)
            })
    ) {
        // 原始图片
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current).data(block.content.content)
                .crossfade(animate).allowHardware(!offscreen).build(),
            contentDescription = null,
            contentScale = ContentScale.Fit,
            modifier = Modifier.fillMaxSize(),
            onState = { state ->
                if (animate && state is AsyncImagePainter.State.Success && !success) {
                    success = true
                    coroutineScope.launch {
                        scale.animateTo(
                            targetValue = 0.85f, animationSpec = tween(durationMillis = 400)
                        )
                        scale.animateTo(
                            targetValue = 1f, animationSpec = tween(durationMillis = 250)
                        )
                    }
                }
            },
        )

        // 标签容器 - 居中显示，允许超出边界
        Box(
            modifier = Modifier
                .fillMaxSize()
                .wrapContentSize(align = Alignment.Center, unbounded = true),
            contentAlignment = Alignment.Center
        ) {
            // 只有当启用标签显示且有brandLink或text时才显示标签
            if (showTags && (block.brandLink != null || block.text != null)) {
                // 圆点和标签的外层容器
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center,
                    modifier = Modifier.wrapContentSize()
                ) {
                    // 圆点图标 - 始终显示，位置固定
                    Image(
                        painter = painterResource(id = R.drawable.icon_tag_point),
                        contentDescription = "Tag point",
                        modifier = Modifier.size(14.dp, 14.dp)
                    )

                    // 圆点和标签间距 - 始终保持，维持布局稳定
                    Spacer(modifier = Modifier.width(4.dp))

                    // 标签容器 - 始终存在，通过透明度控制显示
                    Row(
                        modifier = Modifier
                            .wrapContentSize()
                            .height(24.dp)
                            .background(
                                color = if (showTagContent) Color(0x99222222) else Color.Transparent,
                                shape = RoundedCornerShape(size = 4.dp)
                            )
                            .padding(start = 4.dp, top = 4.dp, end = 4.dp, bottom = 4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        // 品牌链接图标
                        block.brandLink?.let { brandLink ->
                            Box(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .then(if (showTagContent) Modifier else Modifier.alpha(0f)),
                                contentAlignment = Alignment.Center
                            ) {
                                AsyncImage(
                                    model = ImageRequest.Builder(LocalContext.current)
                                        .data(brandLink)
                                        .crossfade(true)
                                        .allowHardware(!offscreen)
                                        .build(),
                                    contentDescription = "Brand logo",
                                    contentScale = ContentScale.Fit,
                                    modifier = Modifier.size(16.dp, 16.dp)
                                )
                            }

                            // 如果同时有品牌图标和文本，添加间距
                            if (block.text != null) {
                                Spacer(modifier = Modifier.width(4.dp))
                            }
                        }

                        // 文本标签
                        block.text?.let { text ->
                            Box(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .then(if (showTagContent) Modifier else Modifier.alpha(0f)),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = text,
                                    fontSize = 12.sp,
                                    color = Color.White,
                                    fontFamily = AppThemeFontFamily.Inter,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                    lineHeight = 12.sp
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun TextBlock(
    block: MoodboardContentBlock,
    zoom: Float = 1f,
    fontZoom: Float = 1f,
) {
    val offsetX = (block.position.x ?: 0f) * zoom
    val offsetY = (block.position.y ?: 1f) * zoom
    val blockWidth = (block.size.width ?: 1f) * zoom
    val blockHeight = (block.size.height ?: 1f) * zoom

    val fontSize = (block.content.style?.fontSize?.toInt() ?: 24) * fontZoom

    Text(
        block.content.content,
        modifier = Modifier
            .size(blockWidth.dp, blockHeight.dp)
            .offset(x = offsetX.dp, y = offsetY.dp)
            .zIndex((block.position.z ?: 0f)),
        textAlign = TextAlign.Center,
        overflow = TextOverflow.Visible,
        fontSize = fontSize.sp,
        lineHeight = (fontSize * 1.1f).sp,
        fontFamily = getFontFamily(block.content.style?.fontFamily),
        color = Color((block.content.style?.color?.trim() ?: "#000000").toColorInt()),
    )
}

fun getFontFamily(fontName: String? = null): FontFamily {
    return when (fontName) {
        "IBM Plex Mono" -> AppThemeFontFamily.IbmPlexMono
        "DM Serif Display" -> AppThemeFontFamily.DmSerifDisplay
        "DM Serif Text" -> AppThemeFontFamily.DmSerifText
        "Outfit" -> AppThemeFontFamily.Outfit
        "Sonsie One" -> AppThemeFontFamily.SonsieOne
        "Lobster" -> AppThemeFontFamily.Lobster
        "Inter" -> AppThemeFontFamily.Inter
        else -> AppThemeFontFamily.Inter
    }
}
