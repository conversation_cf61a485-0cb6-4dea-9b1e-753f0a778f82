package one.srp.gensmo.data.repository

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import one.srp.core.network.model.PublishPostRequest
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.data.repository.utils.parseOrThrow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PublishRepository @Inject constructor() {

    /**
     * 发布帖子到feed
     * @param feedType 内容类型，如 "try_on" 或 "collage"
     * @param documentId 文档ID，对应moodboard_id或try_on_task_id
     * @param title 帖子标题
     * @param description 帖子描述
     */
    fun publishPost(
        feedType: String,
        documentId: String,
        title: String,
        description: String
    ): Flow<Result<Unit>> = flow {
        emit(runCatching {
            Timber.d("发布帖子: feedType=$feedType, documentId=$documentId, title=$title")
            
            val request = PublishPostRequest(
                feedType = feedType,
                documentId = documentId,
                title = title,
                description = description
            )
            
            parseOrThrow { UserService.api.publishPost(request) }
        })
    }.flowOn(Dispatchers.IO)

    /**
     * 发布moodboard到feed
     */
    fun publishMoodboard(
        documentId: String,
        title: String,
        description: String
    ): Flow<Result<Unit>> = publishPost("collage", documentId, title, description)

    /**
     * 发布try-on任务到feed
     */
    fun publishTryOnTask(
        documentId: String,
        title: String,
        description: String
    ): Flow<Result<Unit>> = publishPost("try_on", documentId, title, description)

    /**
     * 从feed中删除已发布的帖子
     * @param feedType 内容类型，如 "try_on" 或 "collage"
     * @param documentId 文档ID，对应moodboard_id或try_on_task_id
     * @param title 帖子标题
     * @param description 帖子描述
     */
    fun deletePublishedPost(
        feedType: String,
        documentId: String,
        title: String,
        description: String
    ): Flow<Result<Unit>> = flow {
        emit(runCatching {
            Timber.d("删除已发布帖子: feedType=$feedType, documentId=$documentId, title=$title")

            val request = PublishPostRequest(
                feedType = feedType,
                documentId = documentId,
                title = title,
                description = description
            )

            parseOrThrow { UserService.api.deletePublishedPost(request) }
        })
    }.flowOn(Dispatchers.IO)

    /**
     * 删除已发布的moodboard
     */
    fun deleteMoodboardFromFeed(
        documentId: String,
        title: String,
        description: String
    ): Flow<Result<Unit>> = deletePublishedPost("collage", documentId, title, description)

    /**
     * 删除已发布的try-on任务
     */
    fun deleteTryOnTaskFromFeed(
        documentId: String,
        title: String,
        description: String
    ): Flow<Result<Unit>> = deletePublishedPost("try_on", documentId, title, description)
}
