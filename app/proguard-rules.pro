# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 增强安全性的 ProGuard 配置
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# 启用优化
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

# 混淆类名、方法名、字段名
-repackageclasses ''

# 移除日志
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 移除 Timber 日志
-assumenosideeffects class timber.log.Timber {
    public static *** d(...);
    public static *** e(...);
    public static *** i(...);
    public static *** v(...);
    public static *** w(...);
}

# 隐藏源文件名
-renamesourcefileattribute SourceFile

# 保留行号信息但隐藏原始文件名（便于调试崩溃）
-keepattributes SourceFile,LineNumberTable

# 保留必要的注解
-keepattributes RuntimeVisibleAnnotations,RuntimeInvisibleAnnotations,RuntimeVisibleParameterAnnotations,RuntimeInvisibleParameterAnnotations

# 保留反射相关
-keepattributes *Annotation*
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keepattributes LocalVariableTable,LocalVariableTypeTable

# 防止混淆导致的运行时错误
-dontoptimize
-dontpreverify

# 保留序列化相关
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# 保留 Kotlin 相关
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**
-keepclassmembers class **$WhenMappings {
    <fields>;
}
-keepclassmembers class kotlin.Metadata {
    public <methods>;
}

# 保留 Kotlin 协程相关
-keep class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**
-keep class kotlin.coroutines.** { *; }
-dontwarn kotlin.coroutines.**

# 保留所有 Companion 对象
-keepclassmembers class * {
    *** Companion;
}
-keep class **$Companion { *; }

# 保留 suspend 函数和方法
-keepclassmembers class * {
    <methods>;
}

# 保留 OkHttp 相关
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**
-keep class okio.** { *; }

# 保留 OkHttp 的内部类和接口
-keep class okhttp3.internal.** { *; }
-keep interface okhttp3.internal.** { *; }

# 保留 OkHttp 的拦截器
-keep class * implements okhttp3.Interceptor { *; }

# 保留 OkHttp 的证书相关
-keep class okhttp3.internal.platform.** { *; }
-keep class okhttp3.internal.tls.** { *; }

# 保留网络安全相关
-keep class java.security.** { *; }
-keep class javax.net.ssl.** { *; }
-keep class javax.security.** { *; }

# 保留网络连接相关
-keep class java.net.** { *; }
-dontwarn java.net.**

# 保留 HTTP 相关
-keep class java.net.http.** { *; }
-dontwarn java.net.http.**

# 保留 OkHttp Logging Interceptor
-keep class okhttp3.logging.** { *; }
-dontwarn okhttp3.logging.**

# 保留自定义的转换器和拦截器
-keep class one.srp.core.network.clients.converters.NullOnEmptyConverterFactory { *; }
-keep class one.srp.gensmo.data.remote.utils.ClientBuilder** { *; }

# 保留环境配置相关
-keep class one.srp.gensmo.utils.env.MetaInfo { *; }

# 保留 DataStore 相关
-keep class one.srp.gensmo.data.store.** { *; }
-keep class androidx.datastore.** { *; }
-dontwarn androidx.datastore.**

# 保留 Retrofit 和网络相关
-keepattributes Signature, InnerClasses, EnclosingMethod
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# 保留 Retrofit 核心类
-keep class retrofit2.** { *; }
-keep interface retrofit2.** { *; }
-dontwarn retrofit2.**

# 保留所有使用 Retrofit 注解的接口和方法
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# 保留 Retrofit 转换器
-keep class * implements retrofit2.Converter { *; }
-keep class * implements retrofit2.Converter$Factory { *; }

# 保留 Retrofit 调用适配器
-keep class * implements retrofit2.CallAdapter { *; }
-keep class * implements retrofit2.CallAdapter$Factory { *; }

# 保留 Kotlinx Serialization Converter
-keep class com.jakewharton.retrofit2.converter.kotlinx.serialization.** { *; }
-dontwarn com.jakewharton.retrofit2.converter.kotlinx.serialization.**

-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.annotation.**
-dontwarn kotlin.Unit
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# 保留 Kotlinx Serialization
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt
-keep,includedescriptorclasses class one.srp.gensmo.**$$serializer { *; }
-keepclassmembers class one.srp.gensmo.** {
    *** Companion;
}
-keepclasseswithmembers class one.srp.gensmo.** {
    kotlinx.serialization.KSerializer serializer(...);
}

# 保留 Kotlinx Serialization Json 相关
-keep class kotlinx.serialization.json.** { *; }
-keep class kotlinx.serialization.json.JsonNamingStrategy { *; }
-keep class kotlinx.serialization.json.JsonNamingStrategy$* { *; }

# 保留项目的JSON配置
-keep class one.srp.gensmo.data.utils.** { *; }

# 保留 Hilt 相关
-keep class dagger.hilt.** { *; }
-keep class javax.inject.** { *; }
-keep class * extends dagger.hilt.android.lifecycle.HiltViewModel { *; }
-keep @dagger.hilt.InstallIn class * { *; }

# 保留 Compose 相关
-keep class androidx.compose.** { *; }
-keep class * extends androidx.compose.runtime.** { *; }

# Firebase Auth 相关规则
-keep class com.google.firebase.auth.** { *; }
-keep class com.google.firebase.auth.internal.** { *; }
-keep class com.firebase.ui.auth.** { *; }
-keep public class com.google.firebase.auth.internal.GenericIdpActivity

# 保留 Google Identity 相关
-keep class com.google.android.libraries.identity.** { *; }
-keep class androidx.credentials.** { *; }

# 保留 CameraX 相关
-keep class androidx.camera.** { *; }

# 保留 Media3/ExoPlayer 相关
-keep class androidx.media3.** { *; }
-keep interface androidx.media3.** { *; }
-dontwarn androidx.media3.**
-keep class com.google.android.exoplayer2.** { *; }
-keep interface com.google.android.exoplayer2.** { *; }
-dontwarn com.google.android.exoplayer2.**

# 保留 OneSignal
-keep class com.onesignal.** { *; }

# 保留 MLKit
-keep class com.google.mlkit.** { *; }

# 保留 JSBridge
-keep class com.smallbuer.jsbridge.** { *; }

# 保留 Coil
-dontwarn coil3.PlatformContext

# 保留应用的数据类和模型类
-keep class one.srp.gensmo.data.** { *; }
-keep class one.srp.gensmo.ui.** { *; }

# 保留所有API接口和网络相关类
-keep interface one.srp.gensmo.data.remote.** { *; }
-keep class one.srp.gensmo.data.remote.** { *; }
-keep class one.srp.gensmo.data.model.** { *; }
-keep class one.srp.gensmo.data.repository.** { *; }

# 保留R类和资源相关（解决反射访问资源问题）
-keep class one.srp.gensmo.R { *; }
-keep class one.srp.gensmo.R$* { *; }
-keepclassmembers class one.srp.gensmo.R$* {
    public static <fields>;
}

# 保留所有资源ID
-keep class **.R
-keep class **.R$* {
    <fields>;
}

# 保留 AppFlyer 相关类
-keep class one.srp.gensmo.utils.metrics.AppsflyerManager { *; }
-keep class one.srp.gensmo.utils.metrics.DeepLinkCallback { *; }

# 保留 AppFlyer SDK 核心类
-keep class com.appsflyer.** { *; }
-keep interface com.appsflyer.** { *; }
-dontwarn com.appsflyer.**

# 保留 AppFlyer 深链接相关
-keep class com.appsflyer.deeplink.** { *; }
-keep interface com.appsflyer.deeplink.** { *; }

# 保留 Parcelable
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 保留 Serializable
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保留 Kotlin JVM 内部类（通用规则）
-keep class kotlin.jvm.internal.** { *; }

-keep @interface kotlin.Metadata {
  *;
}
-keepattributes RuntimeVisibleAnnotations

