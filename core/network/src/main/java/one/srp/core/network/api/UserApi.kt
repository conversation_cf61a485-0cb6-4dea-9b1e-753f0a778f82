package one.srp.core.network.api

import one.srp.core.network.model.CollectionType
import one.srp.core.network.model.HistoryQueriesRes
import one.srp.core.network.model.PostUserCollectionRes
import one.srp.core.network.model.PostUserProfile
import one.srp.core.network.model.PublishHistoryRes
import one.srp.core.network.model.PublishPostRequest
import one.srp.core.network.model.SavedRes
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface UserApi {
    @GET("/user/check/collect/moodboard")
    suspend fun checkUserCollection(
        @Query("moodboard_id") moodboardId: String,
    ): Response<Boolean>

    @POST("/user/collection/moodboard/{moodboard_id}")
    suspend fun saveUserCollection(
        @Path("moodboard_id") moodboardId: String,
    ): Response<PostUserCollectionRes>

    @DELETE("/user/collection/moodboard/{moodboard_id}")
    suspend fun deleteUserCollection(
        @Path("moodboard_id") moodboardId: String,
    ): Response<PostUserCollectionRes>

    @POST("/user/like")
    suspend fun likePost(
        @Query("document_id") documentId: String,
        @Query("collection_type") collectionType: CollectionType,
    ): Response<Unit>

    @DELETE("/user/like")
    suspend fun unlikePost(
        @Query("document_id") documentId: String,
        @Query("collection_type") collectionType: CollectionType,
    ): Response<Unit>

    @POST("/user/share")
    suspend fun sharePost(
        @Query("document_id") documentId: String,
        @Query("collection_type") collectionType: CollectionType,
    ): Response<Unit>

    @DELETE("/user/share")
    suspend fun unsharePost(
        @Query("document_id") documentId: String,
        @Query("collection_type") collectionType: CollectionType,
    )

    @GET("/user/saved/mixed")
    suspend fun getSaved(
        @Query("page") page: Int,
        @Query("page_size") limit: Int,
    ): Response<SavedRes>

    @POST("/user/saved/mixed")
    suspend fun postSaved(
        @Query("document_id") documentId: String,
        @Query("collection_type") collectionType: CollectionType,
    ): Response<Unit>

    @DELETE("/user/saved/mixed")
    suspend fun deleteSaved(
        @Query("document_id") documentId: String,
        @Query("collection_type") collectionType: CollectionType,
    ): Response<Unit>

    @GET("/user/history/queries")
    suspend fun getHistoryQueries(
        @Query("page") page: Int,
        @Query("page_size") limit: Int,
    ): Response<HistoryQueriesRes>

    @DELETE("/user/history/query/{task_id}")
    suspend fun deleteHistoryQuery(
        @Path("task_id") taskId: String,
    ): Response<Unit>

    @GET("/user/profile")
    suspend fun getUserProfile(
        @Query("user_id") uid: String,
    ): Response<PostUserProfile>

    @POST("/user/profile")
    suspend fun postUserProfile(
        @Body body: PostUserProfile,
    ): Response<Unit>

    @GET("/user/publish/history")
    suspend fun getPublishHistory(
        @Query("page") page: Int = 1,
        @Query("page_size") pageSize: Int = 10,
        @Query("target_user_id") targetUserId: String? = null,
    ): Response<PublishHistoryRes>

    @POST("/user/publish")
    suspend fun publishPost(
        @Body body: PublishPostRequest,
    ): Response<Unit>

    @HTTP(method = "DELETE", path = "user/publish", hasBody = true)
    suspend fun deletePublishedPost(
        @Body body: PublishPostRequest,
    ): Response<Unit>
}