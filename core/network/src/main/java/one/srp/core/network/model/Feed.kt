package one.srp.core.network.model

import kotlinx.serialization.Serializable

@Serializable
data class UserInfo(
    val userImageUrl: String,
    val userName: String,
    var followStatus: String? = null,
)

@Serializable
data class FeedItem(
    val moodboardId: String = "",
    val moodboards: MoodboardEntity? = null,
    val query: String? = null,
    val reasoning: String? = null,
    val image: String? = null,
    val imageUrl: String? = null,
    val remix: Int? = null,
    val createdTime: String? = null,
    val createdUserId: String? = null,

    val userAvatar: String? = null,
    val username: String? = null,

    var userInfo: UserInfo? = null,

    val width: Float? = null,
    val height: Float? = null,
    val lastUpdated: Long? = null,
    val moodboardUrl: String? = null,
    val moodboardType: String? = null,
    var buttonText: String? = null,
    val buttonColor: String? = null,
    val taskId: String? = null,

    val isTryOn: Boolean? = null,
    val type: String? = null,
    val tryOnTaskId: String? = null,
    val tryOnCoverImage: String? = null,
    val products: List<ProductItem>? = null,

    val detailTitle: String? = null,
    val detailDescription: String? = null,
    val detailTag: List<String>? = null,

    var isLiked: Boolean = false,
    var likedCount: Int? = null,
    var sharedCount: Int? = null,
    var isFavorited: Boolean = false,

    val coverImageList: List<FeedCoverItem>? = null,
    val userId: String? = null,
    val tryOnUrl: String? = null,

    var commentCount: Int? = null,
    var recommendId: String? = null,
)

@Serializable
data class FeedCoverItem(
    val taskId: String? = null,
    val moodboardId: String? = null,
    val imageType: String,
    val coverUrl: String? = null,
    val hiddenInFront: Boolean? = null,
    val prompt: String? = null,
    val templateUsed: String? = null,
)

enum class FeedCoverItemType(val value: String) {
    COVER("COVER"), COLLAGE("COLLAGE")
}
